import { createContext, useContext, useCallback, useState } from 'react';
import type { ReactNode } from 'react';
import type { MindMapInstance } from '../types';

interface MindMapContextType {
  mindMap: MindMapInstance | null;
  isLoading: boolean;
  isFullscreen: boolean;
  setMindMap: (mindMap: MindMapInstance | null) => void;
  setIsLoading: (loading: boolean) => void;
  setFullscreen: (fullscreen: boolean) => void;
}

const MindMapContext = createContext<MindMapContextType | null>(null);

export function MindMapProvider({ children }: { children: ReactNode }) {
  const [mindMap, setMindMap] = useState<MindMapInstance | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);

  const setFullscreen = useCallback((fullscreen: boolean) => {
    setIsFullscreen(fullscreen);
    if (fullscreen) {
      document.documentElement.requestFullscreen().catch(console.error);
    } else {
      if (document.fullscreenElement) {
        document.exitFullscreen().catch(console.error);
      }
    }
  }, []);

  const value: MindMapContextType = {
    mindMap,
    isLoading,
    isFullscreen,
    setMindMap,
    setIsLoading,
    setFullscreen
  };

  return (
    <MindMapContext.Provider value={value}>
      {children}
    </MindMapContext.Provider>
  );
}

export const useMindMapContext = () => {
  const context = useContext(MindMapContext);
  if (!context) {
    throw new Error('useMindMapContext must be used within MindMapProvider');
  }
  return context;
}; 