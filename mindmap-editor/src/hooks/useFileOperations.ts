import { useCallback, useState } from 'react';
import type { MindMapInstance } from '../types';
import { toast } from 'sonner';

export function useFileOperations(mindMap: MindMapInstance | null) {
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);

  const handleExport = useCallback(async (format: 'json' | 'png' | 'svg' = 'json') => {
    if (!mindMap) {
      toast.error('思维导图未初始化');
      return;
    }
    
    setIsExporting(true);
    try {
      const data = mindMap.export();
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `mindmap.${format}`;
      a.click();
      URL.revokeObjectURL(url);
      toast.success('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      toast.error('导出失败');
    } finally {
      setIsExporting(false);
    }
  }, [mindMap]);

  const handleImport = useCallback(async (file?: File) => {
    if (!mindMap) {
      toast.error('思维导图未初始化');
      return;
    }

    const processFile = (selectedFile: File) => {
      setIsImporting(true);
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = JSON.parse(e.target?.result as string);
          mindMap.setData(data);
          toast.success('导入成功');
        } catch (error) {
          console.error('导入失败:', error);
          toast.error('导入失败：文件格式错误');
        } finally {
          setIsImporting(false);
        }
      };
      reader.onerror = () => {
        toast.error('文件读取失败');
        setIsImporting(false);
      };
      reader.readAsText(selectedFile);
    };

    if (file) {
      processFile(file);
    } else {
      // 如果没有传入文件，则弹出文件选择对话框
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = '.json';
      input.onchange = (e) => {
        const selectedFile = (e.target as HTMLInputElement).files?.[0];
        if (selectedFile) {
          processFile(selectedFile);
        }
      };
      input.click();
    }
  }, [mindMap]);

  return {
    handleExport,
    handleImport,
    isExporting,
    isImporting
  };
} 