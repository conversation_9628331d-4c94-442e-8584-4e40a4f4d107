import { useCallback } from 'react';
import MindMap from 'simple-mind-map';
import type { MindMapInstance, MindMapData } from '../types';
import { toast } from 'sonner';

export function useMindMap() {
  const initMindMap = useCallback((container: HTMLElement) => {
    if (!container) return null;
    
    const rect = container.getBoundingClientRect();
    if (rect.width === 0 || rect.height === 0) {
      // 如果容器尺寸为0，延迟初始化
      setTimeout(() => initMindMap(container), 100);
      return null;
    }

    try {
      // 初始化思维导图
      const mindMapInstance = new MindMap({
        el: container,
        data: {
          data: {
            text: '根节点',
            expand: true,
            isActive: false,
          },
          children: [
            {
              data: {
                text: '子节点1',
                expand: true,
                isActive: false,
              },
              children: [
                {
                  data: {
                    text: '子子节点1',
                    expand: true,
                    isActive: false,
                  },
                  children: []
                }
              ]
            },
            {
              data: {
                text: '子节点2',
                expand: true,
                isActive: false,
              },
              children: []
            }
          ]
        }
      } as any);

      toast.success('思维导图加载完成');
      return mindMapInstance;
    } catch (error) {
      console.error('思维导图初始化失败:', error);
      toast.error('思维导图加载失败');
      return null;
    }
  }, []);

  const destroyMindMap = useCallback((mindMap: MindMapInstance | null) => {
    if (mindMap) {
      try {
        mindMap.destroy();
      } catch (error) {
        console.error('思维导图销毁失败:', error);
      }
    }
  }, []);

  const resizeMindMap = useCallback((mindMap: MindMapInstance | null) => {
    if (mindMap) {
      try {
        mindMap.resize();
      } catch (error) {
        console.error('思维导图重置大小失败:', error);
      }
    }
  }, []);

  const setMindMapData = useCallback((mindMap: MindMapInstance | null, data: MindMapData) => {
    if (mindMap) {
      try {
        mindMap.setData(data);
      } catch (error) {
        console.error('设置思维导图数据失败:', error);
        toast.error('设置数据失败');
      }
    }
  }, []);

  return {
    initMindMap,
    destroyMindMap,
    resizeMindMap,
    setMindMapData
  };
} 