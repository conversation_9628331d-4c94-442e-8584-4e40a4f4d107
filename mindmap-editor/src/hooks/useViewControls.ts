import { useCallback } from 'react';
import type { MindMapInstance } from '../types';
import { toast } from 'sonner';

export function useViewControls(mindMap: MindMapInstance | null) {
  const zoomIn = useCallback(() => {
    if (!mindMap) {
      toast.error('思维导图未初始化');
      return;
    }
    try {
      mindMap.view.narrow();
      toast.success('放大');
    } catch (error) {
      console.error('放大失败:', error);
      toast.error('放大失败');
    }
  }, [mindMap]);

  const zoomOut = useCallback(() => {
    if (!mindMap) {
      toast.error('思维导图未初始化');
      return;
    }
    try {
      mindMap.view.enlarge();
      toast.success('缩小');
    } catch (error) {
      console.error('缩小失败:', error);
      toast.error('缩小失败');
    }
  }, [mindMap]);

  const resetView = useCallback(() => {
    if (!mindMap) {
      toast.error('思维导图未初始化');
      return;
    }
    try {
      mindMap.view.reset();
      toast.success('重置视图');
    } catch (error) {
      console.error('重置视图失败:', error);
      toast.error('重置视图失败');
    }
  }, [mindMap]);

  return {
    zoomIn,
    zoomOut,
    resetView
  };
} 