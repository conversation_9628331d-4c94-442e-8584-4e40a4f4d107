import type { MindMapSidebarProps } from '../../types';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Keyboard, Settings, HelpCircle } from 'lucide-react';
import { cn } from '../../lib/utils';
import { Button } from '../ui/button';

const shortcuts = [
  { key: 'Tab', description: '添加子节点', icon: '⬇️' },
  { key: 'Enter', description: '添加同级节点', icon: '➡️' },
  { key: 'Delete', description: '删除节点', icon: '🗑️' },
  { key: 'F2', description: '编辑节点', icon: '✏️' },
  { key: '双击', description: '快速编辑', icon: '👆' },
];

export function MindMapSidebar({ className }: MindMapSidebarProps) {
  return (
    <div className={cn('h-full flex flex-col gap-4', className)}>
      {/* 快捷键指南 */}
      <Card className="flex-1 border-0 shadow-xl bg-white/90 dark:bg-gray-800/90 backdrop-blur-md rounded-2xl overflow-hidden">
        <CardHeader className="pb-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
          <CardTitle className="flex items-center gap-3 text-lg">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/50 rounded-xl">
              <Keyboard className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <span className="bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-400 dark:to-indigo-400 bg-clip-text text-transparent font-semibold">
              快捷键
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 sm:space-y-3 p-3 sm:p-4">
          {shortcuts.map((shortcut) => (
            <div
              key={shortcut.key}
              className="flex items-center justify-between p-2 sm:p-3 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-all duration-200 group"
            >
              <div className="flex items-center gap-2 sm:gap-3 min-w-0">
                <span className="text-base sm:text-lg group-hover:scale-110 transition-transform duration-200 flex-shrink-0">{shortcut.icon}</span>
                <span className="text-xs sm:text-sm text-gray-700 dark:text-gray-300 font-medium truncate">
                  {shortcut.description}
                </span>
              </div>
              <kbd className="px-2 sm:px-3 py-1 sm:py-1.5 text-xs bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg font-mono text-gray-600 dark:text-gray-400 shadow-sm flex-shrink-0">
                {shortcut.key}
              </kbd>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* 底部操作区 */}
      <div className="space-y-2 sm:space-y-3">
        <Button
          variant="outline"
          size="sm"
          className="w-full justify-start h-9 sm:h-11 rounded-xl border-gray-200 dark:border-gray-700 bg-white/80 dark:bg-gray-800/80 hover:bg-white dark:hover:bg-gray-700 transition-all duration-200 shadow-sm text-sm"
        >
          <Settings className="w-3 h-3 sm:w-4 sm:h-4 mr-2 sm:mr-3" />
          设置
        </Button>
        <Button
          variant="outline"
          size="sm"
          className="w-full justify-start h-9 sm:h-11 rounded-xl border-gray-200 dark:border-gray-700 bg-white/80 dark:bg-gray-800/80 hover:bg-white dark:hover:bg-gray-700 transition-all duration-200 shadow-sm text-sm"
        >
          <HelpCircle className="w-3 h-3 sm:w-4 sm:h-4 mr-2 sm:mr-3" />
          帮助
        </Button>
      </div>
    </div>
  );
}