import type { MindMapContainerProps } from '../../types';
import { useMindMapContext } from '../../contexts';
import { MindMapCanvas } from './MindMapCanvas';
import { MindMapToolbar } from './MindMapToolbar';
import { MindMapSidebar } from './MindMapSidebar';
import { cn } from '../../lib/utils';
import { useState } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '../ui/button';

export function MindMapContainer({ className }: MindMapContainerProps) {
  const { isFullscreen } = useMindMapContext();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  return (
    <div className={cn(
      'w-full h-full flex flex-col',
      isFullscreen ? 'fixed inset-0 z-50 bg-background p-4' : 'min-h-[85vh]',
      className
    )}>
      {/* 工具栏 */}
      <div className="flex-shrink-0 mb-6">
        <MindMapToolbar />
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex flex-row gap-4 lg:gap-6 overflow-hidden">
        {/* 侧边栏 - 可折叠 */}
        <div className={cn(
          'transition-all duration-300 flex-shrink-0',
          sidebarCollapsed ? 'w-0' : 'w-80'
        )}>
          <div className={cn(
            'h-full overflow-hidden transition-opacity duration-300',
            sidebarCollapsed ? 'opacity-0' : 'opacity-100'
          )}>
            <MindMapSidebar />
          </div>
        </div>

        {/* 折叠按钮 */}
        <div className="flex-shrink-0 flex items-center justify-start">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
            className="h-12 w-12 p-0 rounded-xl border-2 border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 hover:scale-105 transition-all duration-200 shadow-sm hover:shadow-md bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm"
          >
            {sidebarCollapsed ? (
              <ChevronRight className="h-5 w-5 text-gray-600 dark:text-gray-400" />
            ) : (
              <ChevronLeft className="h-5 w-5 text-gray-600 dark:text-gray-400" />
            )}
          </Button>
        </div>

        {/* 思维导图画布 - 占据剩余空间 */}
        <div className="flex-1 min-w-0">
          <MindMapCanvas
            height={isFullscreen ? 'calc(100vh - 140px)' : 'calc(85vh - 140px)'}
          />
        </div>
      </div>
    </div>
  );
}