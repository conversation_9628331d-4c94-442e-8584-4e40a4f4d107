import * as React from "react"
import { cn } from "@/lib/utils"

const ButtonGroup = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    variant?: "default" | "outline" | "glass"
    size?: "sm" | "md" | "lg"
  }
>(({ className, variant = "default", size = "md", ...props }, ref) => {
  const variants = {
    default: "bg-muted/50 border border-border/50",
    outline: "border border-border bg-background/50",
    glass: "glass border border-border/30"
  }
  
  const sizes = {
    sm: "p-1 gap-1 rounded-lg",
    md: "p-1.5 gap-1.5 rounded-xl",
    lg: "p-2 gap-2 rounded-xl"
  }

  return (
    <div
      ref={ref}
      className={cn(
        "flex items-center shadow-soft transition-all duration-200",
        variants[variant],
        sizes[size],
        className
      )}
      {...props}
    />
  )
})
ButtonGroup.displayName = "ButtonGroup"

export { ButtonGroup } 