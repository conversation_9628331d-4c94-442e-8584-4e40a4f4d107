import type { MindMapInstance } from './mindmap';

export interface MindMapCanvasProps {
  className?: string;
  height?: string | number;
  onInitialized?: (mindMap: MindMapInstance) => void;
  onError?: (error: Error) => void;
}

export interface MindMapToolbarProps {
  onExport?: () => void;
  onImport?: () => void;
  onZoomIn?: () => void;
  onZoomOut?: () => void;
  onReset?: () => void;
  onFullscreen?: () => void;
  isLoading?: boolean;
  isExporting?: boolean;
  isImporting?: boolean;
}

export interface MindMapSidebarProps {
  className?: string;
}

export interface MindMapContainerProps {
  className?: string;
}

export interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export interface FileUploadProps {
  onFileSelect: (file: File) => void;
  accept?: string;
  className?: string;
} 