export interface MindMapNode {
  data: {
    text: string;
    expand: boolean;
    isActive: boolean;
    [key: string]: any;
  };
  children: MindMapNode[];
}

export interface MindMapData {
  data: MindMapNode['data'];
  children: MindMapNode[];
}

export interface MindMapConfig {
  el: HTMLElement;
  data: MindMapData;
  theme?: string;
  layout?: string;
  [key: string]: any;
}

export interface ViewControls {
  zoomIn: () => void;
  zoomOut: () => void;
  reset: () => void;
}

export interface FileOperations {
  exportData: (format?: 'json' | 'png' | 'svg') => Promise<void>;
  importData: (file: File) => Promise<void>;
}

export interface MindMapInstance {
  view: {
    narrow: (cx?: any, cy?: any, isTouchPad?: any) => void;
    enlarge: (cx?: any, cy?: any, isTouchPad?: any) => void;
    reset: () => void;
  };
  export: () => any;
  setData: (data: MindMapData) => void;
  destroy: () => void;
  resize: () => void;
} 