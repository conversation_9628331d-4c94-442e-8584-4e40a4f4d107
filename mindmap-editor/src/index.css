
@import "tailwindcss";

@theme {
  --transition-timing-function-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.75rem;
  
  /* 现代化主色调 - 蓝紫渐变系 */
  --primary: 215 100% 60%;
  --primary-foreground: 0 0% 98%;
  --primary-gradient: linear-gradient(135deg, hsl(230 100% 67%) 0%, hsl(270 95% 75%) 100%);
  
  /* 次要色调 */
  --secondary: 215 25% 97%;
  --secondary-foreground: 215 25% 25%;
  --secondary-gradient: linear-gradient(135deg, hsl(315 100% 84%) 0%, hsl(345 100% 69%) 100%);
  
  /* 背景和表面 */
  --background: 0 0% 100%;
  --foreground: 215 25% 15%;
  --surface: 0 0% 100% / 0.8;
  --surface-hover: 0 0% 100% / 0.9;
  
  /* 卡片系统 */
  --card: 0 0% 100%;
  --card-foreground: 215 25% 15%;
  --card-border: 215 20% 90%;
  
  /* 交互元素 */
  --muted: 215 25% 96%;
  --muted-foreground: 215 15% 45%;
  --accent: 215 100% 96%;
  --accent-foreground: 215 100% 25%;
  
  /* 边框和输入 */
  --border: 215 20% 88%;
  --input: 215 20% 88%;
  --ring: 215 100% 60%;
  
  /* 状态色 */
  --destructive: 0 84% 60%;
  --destructive-foreground: 0 0% 98%;
  --success: 142 76% 36%;
  --success-foreground: 0 0% 98%;
  --warning: 38 92% 50%;
  --warning-foreground: 0 0% 98%;
  
  /* 弹出层 */
  --popover: 0 0% 100%;
  --popover-foreground: 215 25% 15%;
  
  /* 侧边栏 */
  --sidebar: 0 0% 99%;
  --sidebar-foreground: 215 25% 15%;
  --sidebar-primary: 215 100% 60%;
  --sidebar-primary-foreground: 0 0% 98%;
  --sidebar-accent: 215 25% 96%;
  --sidebar-accent-foreground: 215 25% 25%;
  --sidebar-border: 215 20% 88%;
  --sidebar-ring: 215 100% 60%;
  
  /* 阴影系统 */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  
  /* 图表色彩 */
  --chart-1: 215 100% 60%;
  --chart-2: 270 95% 75%;
  --chart-3: 315 100% 84%;
  --chart-4: 345 100% 69%;
  --chart-5: 38 92% 50%;
}

.dark {
  /* 深色模式现代化配色 */
  --background: 215 25% 8%;
  --foreground: 215 15% 92%;
  --surface: 215 25% 8% / 0.8;
  --surface-hover: 215 25% 8% / 0.9;
  
  /* 卡片系统 */
  --card: 215 25% 12%;
  --card-foreground: 215 15% 92%;
  --card-border: 215 20% 20%;
  
  /* 主色调 */
  --primary: 215 100% 70%;
  --primary-foreground: 215 25% 8%;
  --primary-gradient: linear-gradient(135deg, hsl(230 100% 75%) 0%, hsl(270 95% 80%) 100%);
  
  /* 次要色调 */
  --secondary: 215 25% 18%;
  --secondary-foreground: 215 15% 85%;
  --secondary-gradient: linear-gradient(135deg, hsl(315 100% 75%) 0%, hsl(345 100% 75%) 100%);
  
  /* 交互元素 */
  --muted: 215 25% 15%;
  --muted-foreground: 215 15% 65%;
  --accent: 215 100% 15%;
  --accent-foreground: 215 100% 85%;
  
  /* 边框和输入 */
  --border: 215 20% 25%;
  --input: 215 20% 25%;
  --ring: 215 100% 70%;
  
  /* 状态色 */
  --destructive: 0 84% 65%;
  --destructive-foreground: 215 25% 8%;
  --success: 142 76% 45%;
  --success-foreground: 215 25% 8%;
  --warning: 38 92% 60%;
  --warning-foreground: 215 25% 8%;
  
  /* 弹出层 */
  --popover: 215 25% 12%;
  --popover-foreground: 215 15% 92%;
  
  /* 侧边栏 */
  --sidebar: 215 25% 10%;
  --sidebar-foreground: 215 15% 92%;
  --sidebar-primary: 215 100% 70%;
  --sidebar-primary-foreground: 215 25% 8%;
  --sidebar-accent: 215 25% 15%;
  --sidebar-accent-foreground: 215 15% 85%;
  --sidebar-border: 215 20% 25%;
  --sidebar-ring: 215 100% 70%;
  
  /* 图表色彩 */
  --chart-1: 215 100% 70%;
  --chart-2: 270 95% 80%;
  --chart-3: 315 100% 75%;
  --chart-4: 345 100% 75%;
  --chart-5: 38 92% 60%;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
    transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
  }
  body {
    @apply bg-background text-foreground antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  .theme-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

@layer utilities {
  /* 现代化阴影系统 */
  .shadow-soft {
    box-shadow: var(--shadow-sm);
  }
  .shadow-soft-md {
    box-shadow: var(--shadow-md);
  }
  .shadow-soft-lg {
    box-shadow: var(--shadow-lg);
  }
  .shadow-soft-xl {
    box-shadow: var(--shadow-xl);
  }
  .shadow-soft-2xl {
    box-shadow: var(--shadow-2xl);
  }
  
  /* 渐变背景 */
  .bg-gradient-primary {
    background: var(--primary-gradient);
  }
  .bg-gradient-secondary {
    background: var(--secondary-gradient);
  }
  
  /* 玻璃态效果 */
  .glass {
    background: hsl(var(--surface));
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
  }
  .glass-hover {
    background: hsl(var(--surface-hover));
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
  }
  
  /* 动画类 */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }
  .animate-slide-up {
    animation: slideUp 0.3s ease-in-out;
  }
  .animate-scale-in {
    animation: scaleIn 0.2s ease-in-out;
  }
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
  .animate-glow {
    animation: glow 2s ease-in-out infinite;
  }
  .animate-shimmer {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  /* 交互状态 */
  .interactive {
    @apply transition-all duration-200 ease-in-out;
  }
  .interactive:hover {
    @apply scale-[1.02] shadow-soft-lg;
  }
  .interactive:active {
    @apply scale-[0.98];
  }

  /* 现代化效果 */
  .glass-effect {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .gradient-border {
    position: relative;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(45deg, #3b82f6, #8b5cf6, #ec4899) border-box;
    border: 2px solid transparent;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent;
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0; 
    transform: translateY(10px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.6);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}