{"allowedTools": ["*"], "disallowedTools": [], "autoApproveTools": true, "dangerouslySkipPermissions": true, "allowedMcpTools": ["*"], "mcpAutoApprove": true, "skpPermissions": true, "permissions": {"allow": ["*", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(*)", "<PERSON><PERSON>(./gradlew:*)", "Bash(find:*)", "Bash(jar tf:*)", "<PERSON><PERSON>(javac:*)", "WebFetch(domain:docs.gradle.org)", "mcp__ide__getDiagnostics", "WebFetch(domain:docs.spring.io)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(ssh:*)", "Bash(ping:*)", "Bash(git fetch:*)", "Bash(ls:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(nslookup:*)", "WebFetch(domain:help.aliyun.com)", "WebFetch(domain:bailian.console.aliyun.com)", "Bash(rg:*)", "Bash(grep:*)", "Ba<PERSON>(unzip:*)"], "deny": []}}